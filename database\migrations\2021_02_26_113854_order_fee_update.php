<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class OrderFeeUpdate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, create the orders table if it doesn't exist
        if (!Schema::hasTable('orders')) {
            Schema::create('orders', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('restorant_id');
                $table->unsignedBigInteger('client_id');
                $table->unsignedBigInteger('driver_id')->nullable();
                $table->unsignedBigInteger('table_id')->nullable();
                $table->unsignedBigInteger('address_id')->nullable();
                $table->float('order_price', 8, 2)->default(0);
                $table->float('delivery_price', 8, 2)->default(0);
                $table->string('payment_method')->nullable();
                $table->string('payment_status')->default('unpaid');
                $table->string('stripe_payment_id')->nullable();
                $table->integer('delivery_method')->default(1); // 1=delivery, 2=pickup, 3=dine-in
                $table->string('delivery_pickup_interval')->nullable();
                $table->text('comment')->nullable();
                $table->float('fee', 8, 2)->default(0);
                $table->float('fee_value', 8, 2)->default(0);
                $table->float('static_fee', 8, 2)->default(0);
                $table->float('vatvalue', 8, 2)->default(0);
                $table->float('discount', 8, 2)->default(0);
                $table->float('payment_processor_fee', 8, 2)->default(0);
                $table->string('id_per_vendor')->default('')->nullable();
                $table->string('md')->default('');
                $table->integer('kds_finished')->default(0);
                $table->string('coupon')->nullable();
                $table->timestamps();
                $table->softDeletes();

                // Foreign key constraints will be added by later migrations when the referenced tables exist
            });
        }

        // Then modify the fee_value column if the table exists and column exists
        if (Schema::hasTable('orders') && Schema::hasColumn('orders', 'fee_value')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->float('fee_value',8,2)->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
