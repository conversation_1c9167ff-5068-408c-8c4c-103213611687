{"scripts": {"dev": "NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "hot": "NODE_ENV=development webpack-dev-server --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "production": "NODE_ENV=production node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "license": "MIT", "devDependencies": {"laravel-mix": "^4.1.2", "postcss": "^7.0.17", "tailwindcss": "^0.6.6", "vue-template-compiler": "^2.6.10"}, "dependencies": {"axios": "^0.18.1", "vue": "^2.5.21"}}