<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSettingaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, create the settings table if it doesn't exist
        if (!Schema::hasTable('settings')) {
            Schema::create('settings', function (Blueprint $table) {
                $table->id();
                $table->string('site_name')->nullable();
                $table->string('site_logo')->nullable();
                $table->string('site_logo_dark')->nullable();
                $table->string('restorant_details_image')->nullable();
                $table->string('restorant_details_cover_image')->nullable();
                $table->string('search')->nullable();
                $table->text('description')->nullable();
                $table->text('header_title')->nullable();
                $table->text('header_subtitle')->nullable();
                $table->integer('delivery')->default(0);
                $table->string('maps_api_key')->nullable();
                $table->text('mobile_info_title')->nullable();
                $table->text('mobile_info_subtitle')->nullable();
                $table->timestamps();
            });
        }

        // Then add the order_fields column if it doesn't exist
        if (Schema::hasTable('settings') && !Schema::hasColumn('settings', 'order_fields')) {
            Schema::table('settings', function (Blueprint $table) {
                $table->text('order_fields')->nullable()->default('');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
