<?php

namespace App\Imports;

use App\Restorant;
use App\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Spatie\Permission\Models\Role;
use App\Events\NewVendor;

class RestoImport implements ToModel, WithHeadingRow
{
    private function createSubdomainFromName($name)
    {
        $cyr = [
            'ж',  'ч',  'щ',   'ш',  'ю',  'а', 'б', 'в', 'г', 'д', 'е', 'з', 'и', 'й', 'к', 'л', 'м', 'н', 'о', 'п', 'р', 'с', 'т', 'у', 'ф', 'х', 'ц', 'ъ', 'ь', 'я',
            'Ж',  'Ч',  'Щ',   'Ш',  'Ю',  'А', 'Б', 'В', 'Г', 'Д', 'Е', 'З', 'И', 'Й', 'К', 'Л', 'М', 'Н', 'О', 'П', 'Р', 'С', 'Т', 'У', 'Ф', 'Х', 'Ц', 'Ъ', 'Ь', 'Я', ];
        $lat = [
            'zh', 'ch', 'sht', 'sh', 'yu', 'a', 'b', 'v', 'g', 'd', 'e', 'z', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'r', 's', 't', 'u', 'f', 'h', 'c', 'y', 'x', 'q',
            'Zh', 'Ch', 'Sht', 'Sh', 'Yu', 'A', 'B', 'V', 'G', 'D', 'E', 'Z', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'F', 'H', 'c', 'Y', 'X', 'Q', ];
        $name = str_replace($cyr, $lat, $name);

        return strtolower(preg_replace('/[^A-Za-z0-9]/', '', $name));
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        //Create the user
        $owner = new User;
        $owner->name = $row['owner_name'];
        $owner->email = $row['owner_email'];
        $owner->phone = $row['owner_phone'];
        $owner->api_token = Str::random(80);

        $owner->password = Hash::make($row['owner_password']);
        $owner->save();

        //Assign role
        $owner->assignRole('owner');

        $restaurant=new Restorant([
            'name' => $row['name'],
            'description' => $row['description'].'',
            'subdomain' => $this->createSubdomainFromName($row['name']),
            'user_id' => $owner->id,
            'lat' => 42.005,
            'lng' => 21.44,
            'address' => $row['address'],
            'phone' => $row['restaurant_phone'],
            'logo' => $row['logo']
        ]);

         //Fire event
         NewVendor::dispatch($restaurant->user,$restaurant);

        return $restaurant;
    }
}
