<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 8, 2);
            $table->string('currency', 3)->default('USD');
            $table->string('period')->default('monthly'); // monthly, yearly
            $table->integer('limit_items')->default(-1); // -1 for unlimited
            $table->integer('limit_orders')->default(-1);
            $table->integer('limit_users')->default(-1);
            $table->boolean('enable_ordering')->default(1);
            $table->boolean('enable_pos')->default(0);
            $table->boolean('enable_whatsapp')->default(0);
            $table->boolean('active')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plans');
    }
}
