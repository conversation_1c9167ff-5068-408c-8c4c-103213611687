<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('site_name')->nullable();
            $table->string('site_logo')->nullable();
            $table->string('site_logo_dark')->nullable();
            $table->string('restorant_details_image')->nullable();
            $table->string('restorant_details_cover_image')->nullable();
            $table->string('search')->nullable();
            $table->text('description')->nullable();
            $table->text('header_title')->nullable();
            $table->text('header_subtitle')->nullable();
            $table->boolean('delivery')->default(0);
            $table->string('maps_api_key')->nullable();
            $table->text('mobile_info_title')->nullable();
            $table->text('mobile_info_subtitle')->nullable();
            $table->string('mobile_app_apple')->nullable();
            $table->string('mobile_app_android')->nullable();
            $table->string('facebook')->nullable();
            $table->string('instagram')->nullable();
            $table->string('twitter')->nullable();
            $table->string('youtube')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settings');
    }
}
