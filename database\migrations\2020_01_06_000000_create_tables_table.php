<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTablesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tables', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('restaurant_id');
            $table->unsignedBigInteger('restoarea_id');
            $table->integer('size')->nullable();
            $table->double('x', 8, 2)->nullable();
            $table->double('y', 8, 2)->nullable();
            $table->double('w', 8, 2)->nullable();
            $table->double('h', 8, 2)->nullable();
            $table->boolean('rounded')->default(0);
            $table->timestamps();
            
            $table->foreign('restaurant_id')->references('id')->on('companies');
            $table->foreign('restoarea_id')->references('id')->on('restoareas');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tables');
    }
}
